name: Azure Static Web Apps CI/CD

on:
  push:
    branches: [ main ]
  pull_request:
    types: [ opened, synchronize, reopened ]
    branches: [ main ]

jobs:
  build_and_deploy:
    runs-on: ubuntu-latest
    steps:

      # 1) Checkout de tu repo (sin SSH ni submódulos)
      - name: Checkout repo
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      # 2) Configurar Git para usar HTTPS + PAT en vez de SSH
      - name: Configurar GitHub PAT para HTTPS
        run: |
          git config --global url."https://${{ secrets.PAT_TOKEN }}@github.com/".insteadOf "https://github.com/"

      # 3) Instalar la versión de Node que necesitas (>=20)
      - name: Setup Node.js 20.x
        uses: actions/setup-node@v3
        with:
          node-version: '20.x'

      - name: Configurar PAT para HTTPS
        run: |
          git config --global url."https://${{ secrets.PAT_TOKEN }}@github.com/".insteadOf "https://github.com/"

      # 4) Instala dependencias (ahora npm podrá clonar tu repo privado vía HTTPS+PAT)
      - name: Install dependencies
        run: npm ci

      # 5) Construye tu aplicación
      - name: Build
        run: npm run build

      # 6) Deploy a Azure Static Web Apps, ya sin build interno de Oryx
      - name: Deploy to Azure Static Web Apps
        uses: Azure/static-web-apps-deploy@v1
        with:
          azure_static_web_apps_api_token: ${{ secrets.AZURE_STATIC_WEB_APPS_API_TOKEN_ASHY_GLACIER_017922303 }}
          repo_token: ${{ secrets.GITHUB_TOKEN }}
          action: upload
          skip_app_build: true
          app_location: '/'                   # si tu app está en la raíz
          api_location: 'swa-db-connections'
          output_location: 'dist'            # carpeta que genera `npm run build`
