// contexts/GameOrchestratorContext.tsx
/**
 * RESPONSABILIDADES:
 * - Coordinación entre servicios (speech, AI)
 * - Flujo de aplicación y estados de UI
 * - Manejo de errores y recuperación
 * - Anuncios y mensajes del sistema
 * - Inicialización de la aplicación
 */
import {
  createContext,
  useContext,
  useCallback,
  useEffect,
  type ReactNode,
  useState,
} from "react";
import { useEnygmaGame } from "./EnygmaGameContext";
import { useSpeechInput } from "./SpeechInputContext";
import { useSpeechOutput } from "./SpeechOutputContext";
import { useGameSpeech } from "../hooks/useSpeechCoordinator";


import type { GameFlowState, GameOrchestratorContextProps } from "../models";

const GameOrchestratorContext = createContext<
  GameOrchestratorContextProps | undefined
>(undefined);

export const useGameOrchestrator = () => {
  const context = useContext(GameOrchestratorContext);
  if (!context) {
    throw new Error(
      "useGameOrchestrator must be used within GameOrchestratorProvider"
    );
  }
  return context;
};

export const GameOrchestratorProvider = ({
  children,
}: {
  children: ReactNode;
}) => {
  // Context dependencies
  const game = useEnygmaGame();
  const speechInput = useSpeechInput();
  const speechOutput = useSpeechOutput();
  const gameSpeech = useGameSpeech();

  // Local state
  const [flowState, setFlowState] = useState<GameFlowState>("idle");
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [setupProgress, setSetupProgress] = useState<number>(0);
  const [lastAction, setLastAction] = useState<(() => Promise<void>) | null>(
    null
  );

  // Computed state
  const isReady =
    flowState !== "idle" && flowState !== "initializing";

  // Indicador de modo silencioso (sin voz)
  const [isSilentMode, setIsSilentMode] = useState<boolean>(false);

  // Initialize the entire app
  const initializeApp = useCallback(async (): Promise<void> => {
    setFlowState("initializing");
    setSetupProgress(0);

    try {
      console.log("ℹ️ [orchestrator] 🚀 Initializing Enygma app");

      // Step 1: Configure speech output (25%) - Con manejo de errores mejorado
      try {
        console.log("ℹ️ [orchestrator] 🔧 Configuring speech output");
        const speechConfigured = await speechOutput.configure("female");
        if (!speechConfigured) {
          console.warn("⚠️ [orchestrator] No se pudo configurar la voz, activando modo silencioso");
          setIsSilentMode(true);
        }
        setSetupProgress(25);
      } catch (speechConfigError) {
        console.warn("⚠️ [orchestrator] Error configurando voz, activando modo silencioso:", speechConfigError);
        setIsSilentMode(true);
        setSetupProgress(25);
      }

      // Step 2: Start speech input (50%) - Con manejo de errores mejorado
      try {
        console.log("ℹ️ [orchestrator] 🎤 Starting speech input");
        speechInput.startListening();
        setSetupProgress(50);
      } catch (speechInputError) {
        console.warn("⚠️ [orchestrator] Error iniciando entrada de voz:", speechInputError);
        // Continuar sin entrada de voz - no es crítico
        setSetupProgress(50);
      }

      // Step 3: Test AI connectivity (75%)
      console.log("ℹ️ [orchestrator] 🤖 Testing AI connectivity");
      // Could add a quick AI health check here
      setSetupProgress(75);

      // Step 4: Welcome message (100%) - Con manejo de errores mejorado
      try {
        console.log("ℹ️ [orchestrator] 👋 Playing welcome message");
        await gameSpeech.speakWelcome(
          "El velo del misterio se alza. Bienvenido a Enygma. ¿Estás listo para el desafío?"
        );
        console.log("✅ [orchestrator] ✅ Welcome message played");
      } catch (welcomeError) {
        console.warn("⚠️ [orchestrator] Error reproduciendo mensaje de bienvenida:", welcomeError);
        // Continuar sin mensaje de bienvenida - no es crítico
      }

      // Step 5: Start background music - Con manejo de errores mejorado
      try {
        await speechOutput.playBackgroundMusic();
        console.log("✅ [orchestrator] 🎵 Background music started");
      } catch (musicError) {
        console.warn("⚠️ [orchestrator] No se pudo iniciar música de fondo:", musicError);
        // No es crítico, la música se puede iniciar después con interacción del usuario
      }

      // Completar inicialización independientemente de errores no críticos
      setSetupProgress(100);
      setFlowState("waiting_for_user_choice");
      console.log("✅ [orchestrator] ✅ App initialized successfully");
    } catch (error) {
      console.error("❌ [orchestrator] Error crítico en inicialización:", error);

      // Aún así, intentar completar la inicialización para que la app funcione
      setSetupProgress(100);
      setFlowState("waiting_for_user_choice");

      // Registrar el error pero no cambiar a estado de error
      // para permitir que la aplicación funcione sin voz
      setError(
        error instanceof Error ? error.message : "Unknown initialization error"
      );
    }
  }, [gameSpeech, speechInput]);

  // Announce game start
  const announceGameStart = useCallback(
    async (mode: "player_vs_ia" | "ia_vs_player"): Promise<void> => {
      try {
        console.log(`ℹ️ [orchestrator] 🎮 Announcing game start: ${mode}`);

        const announcement =
          mode === "player_vs_ia"
            ? "¡Perfecto! Piensa en un personaje del entretenimiento. Yo haré preguntas y tú respondes con Sí, No, Tal vez o No lo sé."
            : "¡Genial! He pensado en un personaje. Haz preguntas que pueda responder con Sí, No, Tal vez o No lo sé.";

        // Speak the announcement (game context handles message storage)
        try {
          // await gameSpeech.speakWelcome(announcement);
          console.log("✅ [orchestrator] ✅ Game start announced");
        } catch (speechError) {
          console.warn("⚠️ [orchestrator] Error anunciando inicio del juego:", speechError);
          // Continuar sin anuncio de voz - no es crítico
        }
      } catch (error) {
        console.error("❌ [orchestrator] Error crítico anunciando inicio del juego:", error);
        // No lanzar el error para permitir que el juego continúe sin voz
      }
    },
    [gameSpeech]
  );

  // Announce game end
  const announceGameEnd = useCallback(
    async (
      result: "win" | "lose" | "draw",
      character?: string
    ): Promise<void> => {
      try {
        // log.info("orchestrator", `🏁 Announcing game end: ${result}`, { character });

        let resultMessage = "";
        if (result === "win") {
          resultMessage = character
            ? `¡Excelente! Adiviné correctamente. El personaje era ${character}. `
            : `¡Felicidades! Has adivinado correctamente. `;
        } else if (result === "lose") {
          resultMessage = `Se acabaron las preguntas. `;
        } else {
          resultMessage = `Empate - ¡Buen juego! `;
        }

        // 🔧 CAMBIO: Usar coordinador de speech
        await gameSpeech.speakResponse(resultMessage);
        // log.success("orchestrator", "✅ Game end announced");
      } catch (error) {
        // log.error("orchestrator", "❌ Failed to announce game end", error);
        throw error;
      }
    },
    [gameSpeech]
  );

  // Start the game flow
  const startGameFlow = useCallback(
    async (mode: "player_vs_ia" | "ia_vs_player"): Promise<void> => {
      setFlowState("initializing");
      setIsProcessing(true);

      try {
        // log.info("orchestrator", `🎮 Starting game flow: ${mode}`);

        // Start the game session
        await game.startNewGame(mode);

        // Announce game start
        await announceGameStart(mode);

        setFlowState("game_active");
        // log.success("orchestrator", "✅ Game flow started");
      } catch (error) {
        // log.error("orchestrator", "❌ Failed to start game", error);
        setError(
          error instanceof Error ? error.message : "Failed to start game"
        );
        setFlowState("error");
      } finally {
        setIsProcessing(false);
      }
    },
    [game, announceGameStart]
  );

  // Handle user interaction (coordinating all services)
  const handleUserInteraction = useCallback(
    async (input: string): Promise<void> => {
      if (flowState !== "game_active" || isProcessing) {
        return;
      }

      setIsProcessing(true);
      setFlowState("processing_response");

      const action = async () => {
        try {
          // Validate input using game logic
          const validation = game.validateUserInput(input);
          if (!validation.isValid) {
            // 🔧 CAMBIO: Usar coordinador de speech
            await gameSpeech.speakValidation(
              validation.suggestion ||
                "No entendí esa respuesta. Inténtalo de nuevo."
            );
            return;
          }

          // Process the game turn
          await processGameTurn(input);

          // Check if game ended and handle accordingly
          if (game.session?.phase === "finished") {
            await handleGameEnd(game.session);
          } else {
            setFlowState("game_active");
          }
        } catch (error) {
          // 🔧 CAMBIO: Usar coordinador de speech
          await gameSpeech.speakValidation(
            "Lo siento, hubo un error. ¿Puedes repetir?"
          );
          setFlowState("game_active");
        }
      };

      setLastAction(() => action);
      await action();
      setIsProcessing(false);
    },
    [flowState, isProcessing, game, gameSpeech]
  );

  // Coordinated speak and wait
  const speakAndWaitForResponse = useCallback(
    async (message: string, expectedResponses?: string[]): Promise<string> => {
      // log.info("orchestrator", "🗣️ Speaking and waiting for response", {
      //   message: message.substring(0, 50),
      // });

      // Speak the message
      // 🔧 CAMBIO: Usar coordinador de speech
      await gameSpeech.speakQuestion(message);

      // 🔧 FIX: Usar waitForValidResponse en lugar de waitForResponse
      if (expectedResponses && expectedResponses.length > 0) {
        // Si hay respuestas específicas esperadas, usar waitForCustomResponse
        const response =
          await speechInput.waitForCustomResponse(expectedResponses);
        // log.info("orchestrator", "👂 Received specific response", { response });
        return response;
      } else {
        // Si no hay respuestas específicas, esperar cualquier respuesta válida del juego
        const response = await speechInput.waitForValidResponse();
        // log.info("orchestrator", "👂 Received valid game response", {
        //   response,
        // });
        return response.toString(); // Convertir GameResponseType a string
      }
    },
    [gameSpeech, speechInput]
  );

  // Process a game turn
  const processGameTurn = useCallback(
    async (userInput: string): Promise<void> => {
      if (!game.session) return;

      // log.info("orchestrator", "⚙️ Processing game turn", { input: userInput });

      if (game.playerRole === "guesser") {
        // User is asking a question
        await game.askQuestion(userInput);

        // 🎤 AI responses are now automatically narrated by AIService
        // No need for manual speech calls here
      } else {
        // User is answering a question
        const validatedResponse = speechInput.validateGameResponse(userInput);
        if (validatedResponse !== "invalid") {
          await game.respondToQuestion(validatedResponse);

          // 🎤 AI responses are now automatically narrated by AIService
          // No need for manual speech calls here
        }
      }
    },
    [game, gameSpeech, speechInput]
  );

  // Handle game end
  const handleGameEnd = useCallback(
    async (session: any): Promise<void> => {
      setFlowState("showing_results");

      try {
        // log.info("orchestrator", "🏁 Handling game end", {
        //   winner: session.winner,
        // });

        // Determine result and character
        let result: "win" | "lose" | "draw" = "draw";
        let character: string | undefined;

        if (session.winner === "ai") {
          result = "win";
          character = session.finalGuess;
        } else if (session.winner === "user") {
          result = "win";
          character = session.currentCharacter;
        } else {
          result = "lose";
          character = session.finalGuess || session.currentCharacter;
        }

        // Announce game end
        await announceGameEnd(result, character);
      } catch (error) {
        // log.error("orchestrator", "❌ Failed to handle game end", error);
      }
    },
    [announceGameEnd]
  );

  // End game flow
  const endGameFlow = useCallback(async (): Promise<void> => {
    // log.info("orchestrator", "🛑 Ending game flow");

    try {
      speechInput.stopListening();
      speechInput.clearTranscription();

      // 🔧 CAMBIO: Usar coordinador de speech
      await gameSpeech.speakWelcome(
        "¡Gracias por jugar a Enygma! ¿Te gustaría jugar otra partida?"
      );

      setFlowState("waiting_for_user_choice");
    } catch (error) {
      // log.error("orchestrator", "❌ Failed to end game flow", error);
    }
  }, [speechInput, gameSpeech]);

  // Error recovery
  const recoverFromError = useCallback(() => {
    // log.info("orchestrator", "🔄 Recovering from error");
    setError(null);
    setFlowState("waiting_for_user_choice");
  }, []);

  // Retry last action
  const retryLastAction = useCallback(async (): Promise<void> => {
    if (lastAction) {
      // log.info("orchestrator", "🔄 Retrying last action");
      try {
        await lastAction();
        setError(null);
      } catch (error) {
        // log.error("orchestrator", "❌ Retry failed", error);
      }
    }
  }, [lastAction]);

  // Auto-initialize on mount - DISABLED: Let WelcomeScreen handle initialization
  // useEffect(() => {
  //   if (flowState === "idle") {
  //     initializeApp();
  //   }
  // }, [initializeApp, flowState]);

  // Listen for transcription events
  useEffect(() => {
    const removeListener = speechInput.addEventListener?.((event: any) => {
      if (
        event.type === "transcription" &&
        event.normalized &&
        flowState === "game_active"
      ) {
        handleUserInteraction(event.normalized);
      }
    });

    return removeListener;
  }, [speechInput, handleUserInteraction, flowState]);

  return (
    <GameOrchestratorContext.Provider
      value={{
        flowState,
        isProcessing,
        error,
        initializeApp,
        startGameFlow,
        handleUserInteraction,
        endGameFlow,
        speakAndWaitForResponse,
        processGameTurn,
        handleGameEnd,
        announceGameStart,
        announceGameEnd,
        recoverFromError,
        retryLastAction,
        isReady,
        setupProgress,
        isSilentMode,
      }}
    >
      {children}
    </GameOrchestratorContext.Provider>
  );
};
