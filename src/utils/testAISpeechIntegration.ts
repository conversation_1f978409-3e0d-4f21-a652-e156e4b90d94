/**
 * ========================================================================
 * AI SPEECH INTEGRATION TEST UTILITY
 * ========================================================================
 * 
 * Utility functions to test and verify that AI-generated content is
 * properly being narrated through the Azure TTS system.
 * ========================================================================
 */

import { aiContentSpeechWrapper } from "../services/AIContentSpeechWrapper";
import { aiService } from "../services/AIService";
import { speechCoordinator } from "../services/SpeechCoordinator";

// ========== TEST FUNCTIONS ==========

/**
 * Test basic speech integration
 */
export async function testBasicSpeechIntegration(): Promise<boolean> {
  console.log("🧪 Testing basic speech integration...");
  
  try {
    // Test speech coordinator
    await speechCoordinator.speakInfo("Probando integración básica de speech");
    console.log("✅ Speech coordinator working");
    
    // Test AI content speech wrapper
    const isWorking = await aiContentSpeechWrapper.testSpeechIntegration();
    console.log(`✅ AI Content Speech Wrapper: ${isWorking ? 'Working' : 'Failed'}`);
    
    return isWorking;
  } catch (error) {
    console.error("❌ Basic speech integration test failed:", error);
    return false;
  }
}

/**
 * Test character generation with speech
 */
export async function testCharacterGenerationSpeech(): Promise<boolean> {
  console.log("🧪 Testing character generation with speech...");
  
  try {
    // Enable auto-speech
    aiContentSpeechWrapper.enableCharacterAnnouncements();
    
    // Generate a character (should automatically announce it)
    const character = await aiContentSpeechWrapper.generateCharacterWithAnnouncement();
    
    if (character) {
      console.log(`✅ Character generated and announced: ${character}`);
      return true;
    } else {
      console.warn("⚠️ Character generation returned null");
      return false;
    }
  } catch (error) {
    console.error("❌ Character generation speech test failed:", error);
    return false;
  }
}

/**
 * Test AI response generation with speech
 */
export async function testAIResponseSpeech(): Promise<boolean> {
  console.log("🧪 Testing AI response generation with speech...");
  
  try {
    // Enable auto-speech
    aiContentSpeechWrapper.enableResponseNarration();
    
    // Generate an AI response (should automatically narrate it)
    const response = await aiContentSpeechWrapper.generateResponseWithSpeech(
      "Hola, ¿estás listo para jugar?",
      "ia_vs_player"
    );
    
    if (response.ok && response.output) {
      console.log(`✅ AI response generated and narrated: ${response.output.substring(0, 50)}...`);
      return true;
    } else {
      console.warn("⚠️ AI response generation failed");
      return false;
    }
  } catch (error) {
    console.error("❌ AI response speech test failed:", error);
    return false;
  }
}

/**
 * Test speech configuration controls
 */
export async function testSpeechConfiguration(): Promise<boolean> {
  console.log("🧪 Testing speech configuration controls...");
  
  try {
    // Test enabling/disabling
    aiContentSpeechWrapper.disableAllSpeech();
    console.log("✅ Speech disabled");
    
    aiContentSpeechWrapper.enableAllSpeech();
    console.log("✅ Speech enabled");
    
    // Test individual controls
    aiContentSpeechWrapper.disableCharacterAnnouncements();
    aiContentSpeechWrapper.enableCharacterAnnouncements();
    console.log("✅ Character announcement controls working");
    
    aiContentSpeechWrapper.disableResponseNarration();
    aiContentSpeechWrapper.enableResponseNarration();
    console.log("✅ Response narration controls working");
    
    // Test configuration retrieval
    const config = aiContentSpeechWrapper.getConfig();
    console.log("✅ Configuration retrieved:", config);
    
    return true;
  } catch (error) {
    console.error("❌ Speech configuration test failed:", error);
    return false;
  }
}

/**
 * Run all tests
 */
export async function runAllAISpeechTests(): Promise<void> {
  console.log("🚀 Starting AI Speech Integration Tests...");
  
  const results = {
    basicIntegration: await testBasicSpeechIntegration(),
    characterGeneration: await testCharacterGenerationSpeech(),
    aiResponse: await testAIResponseSpeech(),
    configuration: await testSpeechConfiguration(),
  };
  
  console.log("📊 Test Results:", results);
  
  const allPassed = Object.values(results).every(result => result === true);
  
  if (allPassed) {
    console.log("🎉 All AI Speech Integration tests passed!");
  } else {
    console.warn("⚠️ Some AI Speech Integration tests failed");
  }
  
  return;
}

/**
 * Quick test for development
 */
export async function quickSpeechTest(): Promise<void> {
  console.log("⚡ Quick AI Speech Test...");
  
  try {
    // Test if auto-speech is enabled
    const isEnabled = aiService.isAutoSpeechEnabled();
    console.log(`Auto-speech enabled: ${isEnabled}`);
    
    // Test wrapper configuration
    const config = aiContentSpeechWrapper.getConfig();
    console.log("Wrapper config:", config);
    
    // Test basic speech
    await speechCoordinator.speakInfo("Prueba rápida de speech");
    
    console.log("✅ Quick test completed");
  } catch (error) {
    console.error("❌ Quick test failed:", error);
  }
}

// ========== BROWSER CONSOLE HELPERS ==========
// These functions can be called from the browser console for manual testing

declare global {
  interface Window {
    testAISpeech: {
      runAll: () => Promise<void>;
      quick: () => Promise<void>;
      basic: () => Promise<boolean>;
      character: () => Promise<boolean>;
      response: () => Promise<boolean>;
      config: () => Promise<boolean>;
    };
  }
}

// Expose test functions to browser console
if (typeof window !== 'undefined') {
  window.testAISpeech = {
    runAll: runAllAISpeechTests,
    quick: quickSpeechTest,
    basic: testBasicSpeechIntegration,
    character: testCharacterGenerationSpeech,
    response: testAIResponseSpeech,
    config: testSpeechConfiguration,
  };
}
