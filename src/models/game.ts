// ========== TIPOS BÁSICOS DEL JUEGO ==========
export type GameMode = "player_vs_ia" | "ia_vs_player";

/**
 * Fases del juego que determinan qué acciones están disponibles.
 * - setup: Preparación inicial (selección de modo, personaje)
 * - questioning: Fase de preguntas (usuario hace preguntas o responde)
 * - guessing: Fase de suposiciones (usuario intenta adivinar el personaje)
 * - finished: Juego terminado (se muestra resultado)
 */
export type GamePhase = "setup" | "questioning" | "guessing" | "finished";

/**
 * Rol del jugador en la sesión actual
 * - guesser: El jugador hace preguntas (modo "ia_vs_player")
 * - answerer: El jugador responde preguntas (modo "player_vs_ia")
 */
export type PlayerRole = "guesser" | "answerer";

/**
 * Estructura principal de una sesión de juego
 * Contiene todo el estado necesario para una partida completa
 */
export interface GameSession {
  /** Identificador único de la sesión */
  id: string;
  /** Modo de juego seleccionado */
  mode: GameMode;
  /** Fase actual del juego */
  phase: GamePhase;
  /** Momento de inicio del juego */
  startTime: Date;
  /** Momento de finalización (si ha terminado) */
  endTime?: Date;

  // === ESTADO DEL JUEGO ===
  /** Número de preguntas realizadas */
  questionCount: number;
  /** Máximo de preguntas permitidas */
  maxQuestions: number;
  /** Personaje actual (si está definido) */
  currentCharacter?: string;
  /** Nivel de confianza de la IA (0-100) */
  aiConfidence: number;
  /** Rol del jugador en esta sesión */
  playerRole: PlayerRole;
  /** Historial completo de mensajes */
  messages: GameMessage[];

  // === RESULTADO DEL JUEGO ===
  /** Ganador de la partida */
  winner?: "ai" | "user" | "draw";
  /** Última suposición realizada */
  finalGuess?: string;
  /** Si la suposición final fue correcta */
  wasCorrect?: boolean;
}

/**
 * Estructura de un mensaje en el juego
 * Incluye metadatos para análisis y presentación
 */
export interface GameMessage {
  /** ID único del mensaje */
  id: string;
  /** Contenido del mensaje */
  text: string;
  /** Quién envió el mensaje */
  sender: "user" | "ai";
  /** Momento del envío */
  timestamp: Date;
  /** Tipo de mensaje para clasificación */
  type: "question" | "answer" | "guess" | "hint" | "system" | "presentation";
  /** Nivel de confianza (solo para mensajes de IA) */
  confidence?: number;
  /** Respuesta validada (solo para respuestas del usuario) */
  validatedResponse?: "yes" | "no" | "maybe" | "unknown";
  /** Indica si este mensaje cuenta para el límite de preguntas (default: true para questions) */
  countsAsQuestion?: boolean;
}

/**
 * Insights generados por IA para ayudar al jugador
 * Proporciona sugerencias y análisis del progreso
 */
export interface GameInsight {
  /** Preguntas sugeridas por la IA */
  suggestedQuestions: string[];

  /** Progreso en diferentes categorías de información */
  categoryProgress: {
    person: boolean;      // ¿Se preguntó si es una persona?
    profession: boolean;  // ¿Se preguntó sobre profesión?
    appearance: boolean;  // ¿Se preguntó sobre apariencia?
    era: boolean;         // ¿Se preguntó sobre época?
    nationality: boolean; // ¿Se preguntó sobre nacionalidad?
  };

  /** Personajes más probables según el análisis */
  likelyCharacters: string[];

  /** Nivel de confianza general (0-100) */
  confidenceLevel: number;
}

// ========== VALIDACIÓN DE RESPUESTAS ==========
export type GameResponseType = "yes" | "no" | "maybe" | "unknown" | "invalid";

export interface ValidationResult {
  type: GameResponseType;
  confidence: number;
  alternatives: GameResponseType[];
  suggestions?: string[];
  isAmbiguous: boolean;
}

export interface ResponsePattern {
  exact: string[];
  fuzzy: RegExp[];
  confidence: number;
}
