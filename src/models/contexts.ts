import type { ReactNode } from "react";
import type { AppConfig, AppError, AppState, GameFlowState } from "./app";
import type { GameSession, GamePhase, GameResponseType, GameMode, PlayerRole, GameInsight } from "./game";
import type { SpeechOutputState, VoiceGender, MessageType, SpeechMessage } from "./audio";
import type { TranscriptionEvent } from "./speech";
import type { MHCState, MHCCapabilities } from "./services";

// ========== TIPOS DE EVENTOS ==========
export type EventType =
  | 'game:start'
  | 'game:end'
  | 'speech:input'
  | 'speech:output'
  | 'ui:navigate';

export interface GameEvent {
  type: EventType;
  payload?: any;
  timestamp: Date;
}

// ========== CONTEXTO DE APLICACIÓN ==========
export interface AppContextProps {
  // Estado de la aplicación
  appState: AppState;
  isInitialized: boolean;

  // Configuración
  config: AppConfig;
  updateConfig: (newConfig: Partial<AppConfig>) => void;

  // Manejo de errores
  errors: AppError[];
  addError: (error: AppError) => void;
  clearErrors: () => void;
  clearError: (errorId: string) => void;

  // Navegación
  currentView: string;
  navigate: (view: string) => void;

  // Inicialización
  initialize: () => Promise<void>;
  reset: () => void;
}

// ========== CONTEXTO DE JUEGO ==========
export interface EnygmaGameContextProps {
  // Estado actual
  session: GameSession | null;
  currentPhase: GamePhase;
  playerRole: PlayerRole;

  // Acciones del juego
  startNewGame: (mode: GameMode, character?: string) => Promise<void>;
  askQuestion: (question: string, countsAsQuestion?: boolean) => Promise<void>;
  askInitialMessage: (message: string) => Promise<void>;
  respondToQuestion: (response: "yes" | "no" | "maybe" | "unknown") => Promise<void>;
  makeGuess: (character: string) => Promise<boolean>;
  endGame: (reason: "victory" | "defeat" | "timeout" | "quit") => void;

  // Análisis del juego
  getGameInsights: () => GameInsight;
  getSuggestedResponses: () => string[];

  // Propiedades computadas
  canAskQuestion: boolean;
  canMakeGuess: boolean;
  questionsRemaining: number;
  gameProgress: number;

  // Utilidades
  validateUserInput: (input: string) => { isValid: boolean; suggestion?: string };
  getHint: () => string | null;
}

// ========== CONTEXTO DE ORQUESTADOR ==========
export interface GameOrchestratorContextProps {
  // Estado de aplicación
  flowState: GameFlowState;
  isProcessing: boolean;
  error: string | null;
  isReady: boolean;
  setupProgress: number;
  isSilentMode: boolean;

  // Control de flujo
  initializeApp: () => Promise<void>;
  startGameFlow: (mode: "player_vs_ia" | "ia_vs_player") => Promise<void>;
  handleUserInteraction: (input: string) => Promise<void>;
  endGameFlow: () => Promise<void>;
  handleGameEnd: (session: any) => Promise<void>;

  // Funciones de juego
  speakAndWaitForResponse: (message: string, expectedResponses?: string[]) => Promise<string>;
  processGameTurn: (userInput: string) => Promise<void>;
  announceGameStart: (mode: "player_vs_ia" | "ia_vs_player") => Promise<void>;
  announceGameEnd: (result: any) => Promise<void>;

  // Recuperación de errores
  recoverFromError: () => void;
  retryLastAction: () => Promise<void>;
}

// ========== CONTEXTO DE MHC ==========
export interface MHCContextProps {
  // Estado actual
  state: MHCState;

  // Funcionalidad core MHC
  setSucwTimeout: (timeMS: number) => void;
  closeWebView: () => void;
  hideAura: () => void;
  speakAura: (text: string) => void;
  sendAura: (text: string) => void;
  getId: () => string;

  // Operaciones mejoradas
  speakAndHide: (text: string, hideDelay?: number) => Promise<void>;
  setAppTimeout: (seconds: number) => void;

  // Gestión de conexión
  checkConnection: () => Promise<boolean>;
  reconnect: () => Promise<boolean>;
  getDeviceInfo: () => object;

  // Utilidades
  isFeatureAvailable: (feature: keyof MHCCapabilities) => boolean;
  testAllFeatures: () => Promise<MHCCapabilities>;
  reset: () => void;
}

// ========== CONTEXTO DE ENTRADA DE VOZ ==========
export interface SpeechInputState {
  transcription: string | null;
  isListening: boolean;
  isProcessing: boolean;
  confidence: number;
  lastValidatedResponse: GameResponseType | null;
  errorMessage: string | null;
}

export interface SpeechInputContextProps {
  // Estado actual
  state: SpeechInputState;

  // Controles básicos
  startListening: () => void;
  stopListening: () => void;
  clearTranscription: () => void;
  reset: () => void;

  // Testing y simulación
  simulateTranscription: (text: string) => void;

  // Funcionalidad específica del juego
  validateGameResponse: (text: string) => GameResponseType;
  waitForValidResponse: (timeout?: number) => Promise<GameResponseType>;
  waitForCustomResponse: (expectedResponses: string[], timeout?: number) => Promise<string>;

  // Manejo de eventos
  addEventListener: (callback: (event: TranscriptionEvent) => void) => () => void;

  // Utilidades
  normalizeText: (text: string) => Promise<string>;
  getSupportedResponses: () => string[];
  getResponseHelp: () => string;

  // Utilidades mejoradas
  getConfidenceScore: (text: string) => number;
  getAlternativeResponses: (text: string) => GameResponseType[];
  isResponseAmbiguous: (text: string) => boolean;
}

// ========== CONTEXTO DE SALIDA DE VOZ ==========
export interface SpeechOutputContextProps {
  // Estado actual
  state: SpeechOutputState;

  // Basic speech controls
  speak: (text: string) => Promise<void>;
  pause: () => void;
  resume: () => void;
  stop: () => void;

  // Controles de música independientes
  playBackgroundMusic: () => Promise<void>;
  pauseMusic: () => void;
  resumeMusic: () => void;
  stopMusic: () => void;
  setMusicVolume: (volume: number) => void;

  // Controles de narración independientes
  pauseSpeech: () => void;
  resumeSpeech: () => void;
  stopSpeech: () => void;
  setSpeechVolume: (volume: number) => void;

  // Control master
  toggleMute: () => boolean;
  pauseAll: () => void;
  resumeAll: () => void;
  stopAll: () => void;

  // Configuration
  configure: (gender: VoiceGender) => Promise<boolean>;

  // Game-specific speech
  speakGameMessage: (message: string, type: MessageType) => Promise<void>;
  speakWithEmotion: (message: string, emotion: "excited" | "thoughtful" | "confident" | "disappointed") => Promise<void>;

  // Character voices
  setCharacterVoice: (character: string) => Promise<boolean>;
  getVoiceForCharacter: (character: string) => VoiceGender;

  // Queue management
  speakQueue: (messages: string[]) => Promise<void>;
  clearQueue: () => void;
  skipCurrent: () => void;

  // Utilities
  getAvailableVoices: () => string[];
  testVoice: (text?: string) => Promise<void>;
  reset: () => void;

  // Message history
  messageHistory: SpeechMessage[];
  getLastMessage: () => SpeechMessage | null;
  replayLastMessage: () => Promise<void>;
}

// ========== CONTEXTO DE BUS DE EVENTOS ==========
export interface EventBusContextProps {
  emit: (type: EventType, payload?: any) => void;
  subscribe: (type: EventType, callback: (event: GameEvent) => void) => () => void;
  getLastEvent: (type: EventType) => GameEvent | null;
}

// ========== PROPS COMUNES DE CONTEXTOS ==========
export interface ContextProviderProps {
  children: ReactNode;
}
