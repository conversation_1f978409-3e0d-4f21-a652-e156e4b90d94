// ========== MODELS INDEX ==========
// Centralized export of all model types and interfaces

// ========== APP MODELS ==========
export type {
  ViewMode,
  AppState,
  AppConfig,
  AppError,
  GameFlowState
} from "./app";

// ========== AUDIO MODELS ==========
export type {
  AudioType,
  AudioState,
  VoiceGender,
  MessageType,
  PlaybackState,
  SpeechPriority,
  SpeechChannel,
  SpeechType,
  SpeechRequest,
  SpeechOutputState,
  SpeechState,
  SpeechMessage
} from "./audio";

// ========== COMPONENT MODELS ==========
export type {
  ViewProps,
  RulePage,
  RulesViewData,
  RulesViewProps,
  PlayViewProps,
  MainViewProps,
  CluesViewProps,
  LivesViewProps,
  HeaderProps,
  IconProps,
  IconGoBackProps,
  IconHomeProps,
  IconMenuProps,
  IconSoundOnProps,
  WelcomeScreenProps,
  CookieConsentBannerProps,
  UseSpeechCoordinatorReturn,
  BaseComponentProps,
  ClickableProps,
  LoadingProps,
  ErrorProps,
  ButtonProps,
  ModalProps,
  FormProps,
  InputProps
} from "./components";

// ========== CONFIG MODELS ==========
export type {
  GameModeConfig,
  GameModesConfig,
  GameRuleConfig,
  GameRulesConfig,
  AudioConfig,
  APIConfig,
  SpeechConfig,
  TranscriptionConfig,
  ValidationConfig,
  UIConfig,
  DevelopmentConfig,
  AppConfiguration,
  ConfigLoadResult
} from "./config";

// ========== CONTEXT MODELS ==========
export type {
  EventType,
  GameEvent,
  AppContextProps,
  EventBusContextProps,
  GameOrchestratorContextProps,
  MHCContextProps,
  SpeechInputState,
  SpeechInputContextProps,
  SpeechOutputContextProps
} from "./contexts";

// ========== GAME MODELS ==========
export type {
  GameMode,
  GamePhase,
  PlayerRole,
  GameSession,
  GameMessage,
  GameInsight,
  GameResponseType,
  ValidationResult,
  ResponsePattern
} from "./game";

// ========== SERVICE MODELS ==========
export type {
  AIResponse,
  APIPayload,
  IMHC,
  MHCState,
  MHCCapabilities,
  NormalizationOptions,
  SanitizationOptions,
  TestScenario,
  TestResult
} from "./services";

// ========== SPEECH MODELS ==========
export type {
  IVoicesService,
  VoiceParams,
  AudioRequest,
  PerformanceStats,
  CacheEntry,
  RetryRequest,
  TranscriptionResponse,
  TranscriptionEvent
} from "./speech";
