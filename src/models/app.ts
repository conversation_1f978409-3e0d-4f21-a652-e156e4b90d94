// ========== TIPOS DE VISTA Y NAVEGACIÓN ==========
export type ViewMode = "consent" | "welcome" | "main" | "play" | "rules" | "lives" | "clues";

// ========== ESTADOS DE LA APLICACIÓN ==========
export type AppState =
  | "loading"        // Carga inicial de la aplicación
  | "initializing"   // Inicializando contextos y configuración
  | "consent"        // Esperando consentimiento de cookies
  | "welcome"        // Pantalla de bienvenida
  | "ready"          // Aplicación lista para usar
  | "error";         // Error en inicialización o funcionamiento

// ========== CONFIGURACIÓN DE LA APLICACIÓN ==========
/**
 * Configuración principal de la aplicación
 * Contiene todas las URLs y claves de APIs externas necesarias
 */
export interface AppConfig {
  iaApiUrl: string | null;        // URL de la API de IA
  iaApiKey: string | null;        // Clave de autenticación para IA
  speechApiUrl: string | null;    // URL de la API de síntesis de voz
  speechApiKey: string | null;    // Clave para servicio de speech
}

// ========== MANEJO DE ERRORES ==========
/**
 * Representación de un error capturado por la aplicación
 * Incluye contexto y metadatos para debugging
 */
export interface AppError {
  id: string;           // Identificador único del error
  message: string;      // Mensaje descriptivo del error
  timestamp: Date;      // Momento en que ocurrió el error
  context?: string;     // Contexto donde ocurrió (componente, función, etc.)
  error?: Error;        // Objeto Error original (si disponible)
}

// ========== FLUJO DE LA APLICACIÓN ==========
export type GameFlowState =
  | "idle"
  | "initializing"
  | "waiting_for_user_choice"
  | "game_active"
  | "processing_response"
  | "game_finished"
  | "showing_results"
  | "error";
