/**
 * ========================================================================
 * SPEECH COORDINATOR - Coordinador Centralizado de Speech
 * ========================================================================
 *
 * Resuelve los problemas de solapamiento de speech coordinando:
 * - SpeechService (Azure TTS)
 * - AudioManager (Web Audio)
 * - MHC speakAura (Hardware)
 *
 * Características:
 * - Cola de prioridades
 * - Prevención de solapamientos
 * - Coordinación Hardware vs Web
 * - Interrupciones inteligentes
 * ========================================================================
 */

import { speechService } from "./SpeechService";
import { audioManager } from "./AudioManager";
import { transcriptionService } from "./TranscriptionService";
import axios from "axios";

import type { SpeechPriority, SpeechChannel, SpeechType, SpeechRequest, SpeechState } from "../models/audio";

// ========== MAPEO DE TIPOS A PRIORIDADES ==========
const TYPE_TO_PRIORITY: Record<SpeechType, SpeechPriority> = {
  error: "critical",
  validation: "critical",
  game_response: "high",
  victory: "high",
  instruction: "high",
  question: "medium",
  welcome: "medium",
  hint: "medium",
  info: "low",
  effect: "low",
};

const PRIORITY_VALUES: Record<SpeechPriority, number> = {
  critical: 4,
  high: 3,
  medium: 2,
  low: 1,
};

// ========== COORDINADOR PRINCIPAL ==========
class SpeechCoordinator {
  private static instance: SpeechCoordinator;
  private serviceName = "speechCoordinator";

  private state: SpeechState = {
    isPlaying: false,
    currentRequest: null,
    queue: [],
    channel: "auto",
    lastActivity: 0,
  };

  private listeners: Set<(state: SpeechState) => void> = new Set();
  private processingPromise: Promise<void> | null = null;

  private constructor() {
    console.log(`ℹ️ [${this.serviceName}] 🎯 SpeechCoordinator inicializado`);
  }

  public static getInstance(): SpeechCoordinator {
    if (!SpeechCoordinator.instance) {
      SpeechCoordinator.instance = new SpeechCoordinator();
    }
    return SpeechCoordinator.instance;
  }

  // ========== GESTIÓN DE ESTADO ==========
  private updateState(updates: Partial<SpeechState>): void {
    this.state = { ...this.state, ...updates };
    this.listeners.forEach((listener) => listener(this.state));
  }

  public subscribe(listener: (state: SpeechState) => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  public getState(): SpeechState {
    return { ...this.state };
  }

  // ========== API PRINCIPAL ==========
  /**
   * Solicitar speech con coordinación automática
   */
  public async speak(
    text: string,
    type: SpeechType = "info",
    options: {
      priority?: SpeechPriority;
      channel?: SpeechChannel;
      timeout?: number;
      onStart?: () => void;
      onComplete?: () => void;
      onError?: (error: Error) => void;
    } = {}
  ): Promise<void> {
    if (!text.trim()) {
      console.warn(`⚠️ [${this.serviceName}] ⚠️ Texto vacío ignorado`);
      return;
    }

    const priority = options.priority || TYPE_TO_PRIORITY[type];
    const channel = options.channel || this.determineOptimalChannel();

    const request: SpeechRequest = {
      id: `speech-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      text: text.trim(),
      priority,
      type,
      channel,
      timestamp: Date.now(),
      timeout: options.timeout || 10000,
      onStart: options.onStart,
      onComplete: options.onComplete,
      onError: options.onError,
    };

    console.log(
      `ℹ️ [${this.serviceName}] 📝 Nueva solicitud [${priority}/${type}]: ${text.substring(0, 50)}...`
    );

    return this.enqueueRequest(request);
  }

  /**
   * Interrumpir speech actual si es de menor prioridad
   */
  public interrupt(priority: SpeechPriority = "critical"): void {
    if (!this.state.currentRequest) return;

    const currentPriority = PRIORITY_VALUES[this.state.currentRequest.priority];
    const interruptPriority = PRIORITY_VALUES[priority];

    if (interruptPriority > currentPriority) {
      console.log(
        `ℹ️ [${this.serviceName}] ⏹️ Interrumpiendo speech de prioridad ${this.state.currentRequest.priority}`
      );
      this.stopCurrent();
    }
  }

  /**
   * Limpiar cola de speech
   */
  public clearQueue(): void {
    console.log(`ℹ️ [${this.serviceName}] 🧹 Limpiando cola de speech`);
    this.updateState({ queue: [] });
  }

  /**
   * Parar todo speech
   */
  public stopAll(): void {
    console.log(`ℹ️ [${this.serviceName}] 🛑 Parando todo speech`);
    this.stopCurrent();
    this.clearQueue();
  }

  // ========== GESTIÓN DE COLA ==========
  private async enqueueRequest(request: SpeechRequest): Promise<void> {
    // Si hay speech actual de menor prioridad, interrumpir
    if (this.state.currentRequest) {
      const currentPriority =
        PRIORITY_VALUES[this.state.currentRequest.priority];
      const newPriority = PRIORITY_VALUES[request.priority];

      if (newPriority > currentPriority) {
        console.log(
          `ℹ️ [${this.serviceName}] 🔄 Interrumpiendo para prioridad mayor: ${request.priority}`
        );
        this.stopCurrent();
      }
    }

    // Insertar en cola según prioridad
    const queue = [...this.state.queue];
    const insertIndex = queue.findIndex(
      (req) => PRIORITY_VALUES[req.priority] < PRIORITY_VALUES[request.priority]
    );

    if (insertIndex === -1) {
      queue.push(request);
    } else {
      queue.splice(insertIndex, 0, request);
    }

    this.updateState({ queue });

    // Procesar cola si no está procesando
    if (!this.processingPromise) {
      this.processingPromise = this.processQueue();
      await this.processingPromise;
      this.processingPromise = null;
    }
  }

  private async processQueue(): Promise<void> {
    while (this.state.queue.length > 0 && !this.state.isPlaying) {
      const request = this.state.queue.shift()!;
      this.updateState({
        queue: this.state.queue,
        currentRequest: request,
        isPlaying: true,
        lastActivity: Date.now(),
      });

      try {
        await this.executeRequest(request);
      } catch (error) {
        console.error(
          `❌ [${this.serviceName}] ❌ Error ejecutando speech: ${error}`
        );
        request.onError?.(error as Error);
      } finally {
        this.updateState({
          isPlaying: false,
          currentRequest: null,
        });
      }
    }
  }

  // ========== EJECUCIÓN DE SPEECH ==========
  private async executeRequest(request: SpeechRequest): Promise<void> {
    console.log(
      `ℹ️ [${this.serviceName}] 🎯 Ejecutando [${request.channel}/${request.priority}]: ${request.text.substring(0, 50)}...`
    );

    request.onStart?.();

    const startTime = Date.now();

    try {
      switch (request.channel) {
        case "mhc":
          await this.executeMHCSpeech(request);
          break;
        case "web":
          await this.executeWebSpeech(request);
          break;
        case "auto":
          await this.executeAutoSpeech(request);
          break;
      }

      const duration = Date.now() - startTime;
      console.log(
        `✅ [${this.serviceName}] ✅ Speech completado en ${duration}ms`
      );
      request.onComplete?.();
    } catch (error) {
      console.error(`❌ [${this.serviceName}] ❌ Error en speech: ${error}`);
      throw error;
    }
  }

  private async executeMHCSpeech(request: SpeechRequest): Promise<void> {
    if (!transcriptionService.isMHCAvailable()) {
      console.warn(
        `⚠️ [${this.serviceName}] ⚠️ MHC no disponible, fallback a web`
      );
      await this.executeWebSpeech(request);
      return;
    }

    transcriptionService.speakAura(request.text);

    // Simular duración basada en texto (MHC no da feedback de finalización)
    const estimatedDuration = Math.max(2000, request.text.length * 60);
    await new Promise((resolve) => setTimeout(resolve, estimatedDuration));
  }

  private async executeWebSpeech(request: SpeechRequest): Promise<void> {
    try {
      const audioBlob = await speechService.getAudio(request.text);
      await audioManager.playSpeech(audioBlob);
    } catch (error) {
      console.warn(
        `⚠️ [${this.serviceName}] Azure TTS falló, usando modo silencioso:`,
        error
      );

      // 🔧 AÑADIR MÁS CONTEXTO AL ERROR
      if (axios.isAxiosError(error) && error.response?.status === 422) {
        console.error(
          `🔴 [${this.serviceName}] Error 422 - Estructura de datos incorrecta para Azure TTS`
        );
        console.error(
          `🔴 [${this.serviceName}] Revisa la documentación de la API de Azure TTS`
        );
        console.error(`🔴 [${this.serviceName}] URL: ${error.config?.url}`);
        console.error(
          `🔴 [${this.serviceName}] Payload enviado:`,
          error.config?.data
        );
      }

      // Fallback silencioso mejorado
      await this.executeSilentSpeech(request);
    }
  }

  private async executeSilentSpeech(request: SpeechRequest): Promise<void> {
    console.log(
      `ℹ️ [${this.serviceName}] 🔇 Ejecutando speech silencioso: ${request.text.substring(0, 50)}...`
    );

    // Simular duración basada en texto (aproximadamente 150 palabras por minuto)
    const words = request.text.split(" ").length;
    const estimatedDuration = Math.max(1000, (words / 150) * 60 * 1000); // mínimo 1 segundo

    await new Promise((resolve) => setTimeout(resolve, estimatedDuration));

    console.log(`✅ [${this.serviceName}] 🔇 Speech silencioso completado`);
  }

  private async executeAutoSpeech(request: SpeechRequest): Promise<void> {
    // Preferir MHC para speech principal, web para efectos
    const preferMHC = [
      "game_response",
      "question",
      "instruction",
      "welcome",
    ].includes(request.type);

    if (preferMHC && transcriptionService.isMHCAvailable()) {
      await this.executeMHCSpeech(request);
    } else {
      await this.executeWebSpeech(request);
    }
  }

  // ========== UTILIDADES ==========
  private determineOptimalChannel(): SpeechChannel {
    // Si MHC está disponible, preferirlo para speech principal
    if (transcriptionService.isMHCAvailable()) {
      return "auto"; // Permitir que auto decida según el tipo
    }
    return "web";
  }

  private stopCurrent(): void {
    if (this.state.currentRequest) {
      // Parar audio web
      audioManager.pauseSpeech();

      // No podemos parar MHC directamente, pero podemos marcar como interrumpido
      console.log(`ℹ️ [${this.serviceName}] ⏹️ Speech actual interrumpido`);

      this.updateState({
        isPlaying: false,
        currentRequest: null,
      });
    }
  }

  // ========== MÉTODOS DE CONVENIENCIA ==========
  /**
   * Speech para errores críticos (máxima prioridad)
   */
  public async speakError(text: string): Promise<void> {
    return this.speak(text, "error", { priority: "critical" });
  }

  /**
   * Speech para validaciones (máxima prioridad)
   */
  public async speakValidation(text: string): Promise<void> {
    return this.speak(text, "validation", { priority: "critical" });
  }

  /**
   * Speech para respuestas del juego (alta prioridad)
   */
  public async speakGameResponse(text: string): Promise<void> {
    return this.speak(text, "game_response", { priority: "high" });
  }

  /**
   * Speech para preguntas del juego (prioridad media)
   */
  public async speakGameQuestion(text: string): Promise<void> {
    return this.speak(text, "question", { priority: "medium" });
  }

  /**
   * Speech para mensajes de bienvenida (prioridad media)
   */
  public async speakWelcome(text: string): Promise<void> {
    return this.speak(text, "welcome", { priority: "medium" });
  }

  /**
   * Speech para pistas (prioridad media)
   */
  public async speakHint(text: string): Promise<void> {
    return this.speak(text, "hint", { priority: "medium" });
  }

  /**
   * Speech para información general (baja prioridad)
   */
  public async speakInfo(text: string): Promise<void> {
    return this.speak(text, "info", { priority: "low" });
  }

  /**
   * Forzar uso de MHC (si está disponible)
   */
  public async speakMHC(
    text: string,
    type: SpeechType = "info"
  ): Promise<void> {
    return this.speak(text, type, { channel: "mhc" });
  }

  /**
   * Forzar uso de web speech
   */
  public async speakWeb(
    text: string,
    type: SpeechType = "info"
  ): Promise<void> {
    return this.speak(text, type, { channel: "web" });
  }

  /**
   * Speech con callback de finalización
   */
  public async speakWithCallback(
    text: string,
    type: SpeechType,
    onComplete: () => void
  ): Promise<void> {
    return this.speak(text, type, { onComplete });
  }

  /**
   * Verificar si está hablando actualmente
   */
  public isSpeaking(): boolean {
    return this.state.isPlaying;
  }

  /**
   * Obtener información del speech actual
   */
  public getCurrentSpeech(): SpeechRequest | null {
    return this.state.currentRequest;
  }

  /**
   * Obtener número de elementos en cola
   */
  public getQueueLength(): number {
    return this.state.queue.length;
  }
}

// ========== INSTANCIA SINGLETON ==========
export const speechCoordinator = SpeechCoordinator.getInstance();
