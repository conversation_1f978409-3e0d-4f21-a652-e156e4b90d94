import type { AIResponse, APIPayload } from "../models/services";
import type { GameMode } from "../models/game";
import { speechCoordinator } from "./SpeechCoordinator";

// ========== CONFIGURATION ==========
const API_CONFIG = {
  baseURL:
    import.meta.env.VITE_IA_API_URL || "https://dev.dl2discovery.org/llm/",
  apiKey:
    import.meta.env.VITE_IA_API_KEY || "b3df20aa-db3b-49ef-8d3b-4abfac8c1161",
  presets: {
    aura:
      import.meta.env.VITE_IA_PRESETID_IA_VS_PLAYER ||
      "mapp-Test akinator_claude37US",
    user:
      import.meta.env.VITE_IA_PRESETID_PLAYER_VS_IA ||
      "mapp-Test akinator_claude37Bi",
    genCharBot:
      import.meta.env.VITE_IA_PRESETID_GENCHARBOT ||
      "mapp-gen-char-bot",
  },
};

// ========== GAME PROMPTS ==========
const GAME_PROMPTS = {
  player_vs_ia: `
        Eres un juego de adivinanza estilo "Akinator". El usuario debe pensar en un personaje, actor, actriz u otra figura del mundo del entretenimiento (real o ficticio).

        Tu tarea es adivinar quién es haciendo preguntas que el usuario solo puede responder con: "Sí", "Es posible", "No", "No sé".

        Reglas del juego:
        - Solo tú haces preguntas. El usuario responde con una de las opciones anteriores.
        - Haz solo una pregunta por turno. No uses preguntas dobles ni abiertas.
        - No repitas preguntas ni hagas variantes de lo que ya sabes.
        - Lleva una lista interna de lo que has aprendido para no preguntar lo mismo.
        - Adapta tus preguntas estratégicamente según las respuestas del usuario.
        - Empieza con preguntas genéricas y así descartas más ideas de golpe.
        - Puedes hacer un máximo de 20 preguntas.
        - Cuando creas tener suficiente información, haz una suposición: "Creo que estás pensando en [nombre del personaje]. ¿Es correcto?"

        Ejemplos de preguntas válidas:
        - ¿Tu personaje aparece en películas?
        - ¿Es una persona real?
        - ¿Es mayor de 50 años?
        - ¿Trabaja principalmente en Hollywood?

        Ejemplos inválidos:
        - ¿Tu personaje es real o ficticio? (pregunta doble)
        - ¿Qué tipo de películas hace? (pregunta abierta)
    `,
  ia_vs_player: `
        Eres un juego de adivinanza. Te llamas Genigma, tú vas a pensar en un personaje del mundo del entretenimiento: puede ser un actor, actriz, director o personaje ficticio de cine, televisión, literatura, etc.

        El usuario intentará adivinar en quién estás pensando haciendo preguntas.

    Reglas del juego:
    Tú ya tienes en mente un personaje (real o ficticio).
    Elige uno que sea reconocible y conocido.

    El usuario hará preguntas para adivinar quién es.
    Solo puedes responder con:
    "Sí", "No", "No lo sé".

    No des pistas directas ni nombres.

    No des explicaciones largas.

    El usuario puede hacer un máximo de 20 preguntas.
    Lleva la cuenta internamente (no hace falta que la digas en voz alta, salvo que el usuario lo pida).

    Cómo iniciar:
    Comienza diciendo:

    Ya estoy pensando en un personaje del mundo del entretenimiento. Puedes empezar a hacerme preguntas para intentar adivinar quién es. Solo responderé con “sí”, “no”, “no lo sé”.
    ¡Adelante, hazme tu primera pregunta!

    Si el usuario hace una suposición:
    Si acierta, responde:
    ¡Correcto! Estaba pensando en [nombre del personaje]. ¡Bien hecho!

    Si falla, responde simplemente:
    No, prueba con otro. (Si el usuario está muy cerca de adivinar quién es, anímalo a seguir intentándolo)

    Fin del juego:
    Si el usuario llega a la pregunta 20 sin acertar, di:
    Has hecho 20 preguntas y no lo has adivinado. Yo estaba pensando en [nombre del personaje].

    Al final, ofrece repetir:
    ¿Quieres jugar otra vez? Puedo pensar en otro personaje si quieres.
    `,
};

// ========== AI SERVICE ==========
class AIService {
  private static instance: AIService;
  private sessionId: string | null = null;
  private serviceName = "ai";
  private sesid = "";
  private autoSpeechEnabled = true; // Enable automatic speech for AI responses

  private constructor() {}

  public static getInstance(): AIService {
    if (!AIService.instance) {
      AIService.instance = new AIService();
    }
    return AIService.instance;
  }

  // ========== GETTERS ==========
  public getSessionId(): string | null {
    return this.sessionId;
  }

  public isAutoSpeechEnabled(): boolean {
    return this.autoSpeechEnabled;
  }

  public getPresetForMode(mode: GameMode): string {
    switch (mode) {
      case "player_vs_ia":
        return API_CONFIG.presets.user; // Usuario piensa, IA adivina
      case "ia_vs_player":
        return API_CONFIG.presets.aura; // Aura piensa, usuario adivina
      default:
        return API_CONFIG.presets.user;
    }
  }

  /**
   * Obtiene el personaje guardado en localStorage
   * @returns El personaje guardado o null si no existe
   */
  public getStoredCharacter(): string | null {
    try {
      return localStorage.getItem("enygma_generated_character");
    } catch (error) {
      console.warn(`⚠️ [${this.serviceName}] No se pudo leer localStorage:`, error);
      return null;
    }
  }

  public getApiConfig() {
    return { ...API_CONFIG };
  }

  // ========== AUTO-SPEECH CONTROL ==========
  public enableAutoSpeech(): void {
    this.autoSpeechEnabled = true;
    console.log(`ℹ️ [${this.serviceName}] 🔊 Auto-speech habilitado`);
  }

  public disableAutoSpeech(): void {
    this.autoSpeechEnabled = false;
    console.log(`ℹ️ [${this.serviceName}] 🔇 Auto-speech deshabilitado`);
  }

  /**
   * Temporarily disable auto-speech for a single operation
   * Useful when external systems will handle the speech
   */
  public async generateResponseSilent(
    query: string,
    mode: GameMode,
    character?: string
  ): Promise<AIResponse> {
    const wasEnabled = this.autoSpeechEnabled;
    this.autoSpeechEnabled = false;

    try {
      const result = await this.generateResponse(query, mode, character);
      return result;
    } finally {
      this.autoSpeechEnabled = wasEnabled;
    }
  }

  // ========== PRIVATE METHODS ==========
  /**
   * Automatically narrate AI-generated content using Azure TTS
   * @param text - The text to narrate
   * @param type - The type of content (response, character, etc.)
   */
  private async narrateAIContent(text: string, type: 'response' | 'character' | 'welcome' = 'response'): Promise<void> {
    if (!this.autoSpeechEnabled || !text.trim()) {
      return;
    }

    try {
      console.log(`🎤 [${this.serviceName}] Narrando contenido AI [${type}]: ${text.substring(0, 50)}...`);

      switch (type) {
        case 'character':
          // await speechCoordinator.speakGameResponse(`He pensado en ${text}`);
          console.log(`ℹ️ [${this.serviceName}] Narrando personaje generado: ${text}`);
          break;
        case 'welcome':
          await speechCoordinator.speakWelcome(text);
          break;
        case 'response':
        default:
          await speechCoordinator.speakGameResponse(text);
          break;
      }

      console.log(`✅ [${this.serviceName}] Contenido AI narrado exitosamente`);
    } catch (error) {
      console.warn(`⚠️ [${this.serviceName}] Error narrando contenido AI:`, error);
      // No lanzar error para no interrumpir el flujo principal
    }
  }

  private async makeRequest<T>(endpoint: string, payload?: any): Promise<T> {
    try {
      const url = `${API_CONFIG.baseURL}${endpoint}`;
      const options: RequestInit = {
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Content-Type": "application/json",
          "X-Api-Key": API_CONFIG.apiKey || "",
          "X-MG-Ses": this.sesid,
          "X-Frame-Options": "SAMEORIGIN",
        },
      };

      if (payload) {
        options.method = "POST";
        options.body = JSON.stringify(payload);
      } else {
        options.method = "GET";
      }

      const response = await fetch(url, options);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data as T;
    } catch (error) {
      if (import.meta.env.MODE === "development") {
        // console.error(`🔴 AI API ERROR: ${error}`);
      }
      throw new Error("Error al conectar con la API de IA");
    }
  }

  private buildPayload(query: string, mode: GameMode, character?: string): APIPayload {
    const preset = this.getPresetForMode(mode);
    let preamble = GAME_PROMPTS[mode];

    // Si es modo ia_vs_player y tenemos un personaje, modificar el preamble
    if (mode === "ia_vs_player" && character) {
      preamble = `${GAME_PROMPTS[mode]}

IMPORTANTE: El personaje en el que debes pensar es: ${character}

Responde siempre como si estuvieras pensando en este personaje específico.`;
    }

    return {
      id: {
        ses: this.sessionId || undefined,
        clt: "aura-game-client",
        corr: `game-${Date.now()}`,
      },
      preset,
      query,
      prompt_params: {
        preamble,
      },
      model_params: {
        max_tokens: mode === "ia_vs_player" ? 50 : 200,
        // temperature: mode === "ia_vs_player" ? 0.3 : 0.8,
      },
    };
  }

  // ========== PUBLIC METHODS ==========
  public async generateResponse(
    query: string,
    mode: GameMode,
    character?: string
  ): Promise<AIResponse> {
    console.log(
      `ℹ️ [${this.serviceName}] Generando respuesta para modo ${mode}`,
      {
        query: query.substring(0, 100),
        sessionId: this.sessionId,
      }
    );

    try {
      const payload = this.buildPayload(query, mode, character);
      console.log(`🔍 [${this.serviceName}] Payload construido`, {
        preset: payload.preset,
        character: character || "none",
      });

      const data = await this.makeRequest<any>("generate", payload);

      if (!data.ok) {
        throw new Error(data.message || "Error en la respuesta de la IA");
      }

      console.log(`✅ [${this.serviceName}] Respuesta recibida`, {
        data: data,
        output: data.output,
        sessionId: data.id?.ses || this.sessionId,
      });

      // Actualizar sessionId si es una nueva sesión
      if (data.id?.ses) {
        const oldSessionId = this.sessionId;
        this.sessionId = data.id.ses;
        console.log(
          `ℹ️ [${this.serviceName}] Session ID actualizado: ${oldSessionId} → ${this.sessionId}`
        );
      }

      console.log(`✅ [${this.serviceName}] Respuesta generada exitosamente`, {
        outputLength: data.output?.length,
        sessionId: this.sessionId,
      });

      // 🎤 AUTO-NARRATE: Automatically speak the AI response
      if (data.output) {
        this.narrateAIContent(data.output, 'response').catch(error => {
          console.warn(`⚠️ [${this.serviceName}] Error en auto-narración:`, error);
        });
      }

      return {
        ok: true,
        output: data.output,
        sessionId: this.sessionId || "",
      };
    } catch (error) {
      console.error(
        `❌ [${this.serviceName}] Error generando respuesta`,
        error
      );
      return {
        ok: false,
        output: "",
        sessionId: this.sessionId || "",
        error: error instanceof Error ? error.message : "Error desconocido",
      };
    }
  }

  public async resetSession(): Promise<boolean> {
    if (!this.sessionId) {
      // console.log("⚠️ AI Service: No hay sesión para resetear");
      return true;
    }

    try {
      // console.log(`🔄 AI Service: Reseteando sesión ${this.sessionId}`);

      const data = await this.makeRequest<any>(`reset/${this.sessionId}`);

      if (data.ok) {
        // console.log("✅ AI Service: Sesión reseteada correctamente");
        this.sessionId = null;
        return true;
      } else {
        throw new Error("Error al resetear sesión");
      }
    } catch (error) {
      // console.error("❌ AI Service: Error al resetear sesión:", error);
      // Limpiar sessionId local independientemente del resultado
      this.sessionId = null;
      return false;
    }
  }

  /**
   * Genera un personaje usando el preset GENCHARBOT
   * @returns El nombre del personaje generado o null si hay error
   */
  public async generateCharacter(): Promise<string | null> {
    console.log(`🎭 [${this.serviceName}] Generando personaje con GENCHARBOT`);

    try {
      const correlationId = `genchar-${Date.now()}`;
      const payload = {
        id: {
          ses: this.sessionId || undefined,
          clt: "aura-game-client",
          corr: correlationId,
        },
        corrid: correlationId,
        preset: API_CONFIG.presets.genCharBot,
        query: "Dime un personaje",
        prompt_params: {
          preamble: "",
        },
        model_params: {
          max_tokens: 50,
        },
      };

      console.log(`🔍 [${this.serviceName}] Generando personaje con preset:`, {
        preset: payload.preset,
      });

      const data = await this.makeRequest<any>("generate", payload);

      if (!data.ok) {
        throw new Error(data.message || "Error al generar personaje");
      }

      let character = data.output?.trim();
      console.log(`✅ [${this.serviceName}] Personaje generado:`, character);

      // Guardar en localStorage
      if (character) {
        try {
          localStorage.setItem("enygma_generated_character", character);
          localStorage.setItem("enygma_character_timestamp", new Date().toISOString());
          console.log(`💾 [${this.serviceName}] Personaje guardado en localStorage`);
        } catch (error) {
          console.warn(`⚠️ [${this.serviceName}] No se pudo guardar en localStorage:`, error);
        }

        // 🎤 AUTO-NARRATE: Automatically announce the generated character
        this.narrateAIContent(character, 'character').catch(error => {
          console.warn(`⚠️ [${this.serviceName}] Error narrando personaje generado:`, error);
        });
      }

      return character || null;
    } catch (error) {
      console.error(`❌ [${this.serviceName}] Error generando personaje:`, error);
      return null;
    }
  }

  public async startNewGame(mode: GameMode, character?: string): Promise<AIResponse | null> {
    console.log(`🎮 [${this.serviceName}] Iniciando nuevo juego en modo: ${mode}`, { character });

    try {
      // Resetear sesión anterior si existe
      if (this.sessionId) {
        console.log(`🔄 [${this.serviceName}] Reseteando sesión anterior: ${this.sessionId}`);
        await this.resetSession();
      }

      // Mensaje específico según el modo
      const initialMessage =
        mode === "player_vs_ia"
          ? "Piensa en alguien... y empezamos cuando quieras."
          : "¡Hola! Estoy listo para adivinar lo que sea. ¿Ya tienes un personaje en mente?";

      const response = await this.generateResponse(initialMessage, mode, character);

      if (response.ok) {
        console.log(`✅ [${this.serviceName}] Juego iniciado exitosamente`, {
          mode,
          sessionId: response.sessionId,
          character: character || "none",
        });

        // 🎤 AUTO-NARRATE: The generateResponse method already handles narration
        // No need to duplicate narration here since generateResponse calls narrateAIContent
      }

      return response;
    } catch (error) {
      // log.error(this.serviceName, "Error en startNewGame", error);
      return null;
    }
  }

  public clearSession(): void {
    this.sessionId = null;
    // console.log("🧹 AI Service: Sesión local limpiada");
  }
}

// ========== SINGLETON EXPORT ==========
export const aiService = AIService.getInstance();
