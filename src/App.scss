// @use "variables";

.text-center {
  display: flex;
  justify-content: center;
}

.loader-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgb(3, 47, 70);
}

#welcome-screen-overlay {
  position: fixed;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 20, 40, 0.95);
  z-index: 9998;
  display: flex;
  justify-content: center;
  align-items: center;

  .welcome-screen {
    max-width: 600px;
    animation: mysticalAppear 1s ease-out forwards;

    h1 {
      font-size: clamp(28px, 5vw, 48px);
      font-weight: 600;
      color: #88FFD5;
      margin-bottom: 40px;
      line-height: 1.3;
      text-shadow: 0 0 20px rgba(136, 255, 213, 0.5);
    }

    p {
      font-size: clamp(18px, 3vw, 24px);
      color: #e0e0e0;
      margin-bottom: 60px;
      line-height: 1.6;
      max-width: 500px;
      margin: 0 auto 60px auto;
      opacity: 0.9;
      text-align: center;
    }

    .disclaimer-text {
      margin-top: 20px;
      font-size: 14px;
      color: #94a3b8;
      opacity: 0.8;
      animation: fadeIn 0.5s ease-in;
    }
  }
}

@keyframes mysticalAppear {
  from {
    opacity: 0;
    transform: scale(0.9) rotateY(10deg);
  }
  to {
    opacity: 1;
    transform: scale(1) rotateY(0deg);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 0.8; }
}

.game-container {
  position: relative;

  .background {
    width: 100vw;
    height: 100vh;
    object-fit: cover;
    position: absolute;
  }

  .board {
    position: relative;
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .content {
      flex: 1;
      display: flex;
      margin: 0 58px 48px;

      .menu-left {
        flex-basis: 183px;
        flex-shrink: 0;
        flex-grow: 0;
        align-self: end;

        div {
          text-align: center;
          font-weight: 700;
          font-style: Bold;
          font-size: 22px;
          line-height: 24px;
          letter-spacing: 0%;
          text-align: center;
          vertical-align: middle;
        }

        img {
          width: 121px;
          height: 121px;
          margin: 0 auto;
        }
      }

      .game {
        flex-grow: 1;
        flex-shrink: 1;
        flex-basis: auto;

        display: flex;
        flex-direction: column;
        justify-content: space-around;
        align-items: center;

        .game-header {
          text-align: center;

          h1 {
            font-family: Playfair Display;
            font-weight: 600;
            font-style: SemiBold;
            font-size: 80px;
            line-height: 180%;
            letter-spacing: 0%;
          }

          p {
            font-family: On Air;
            font-weight: 400;
            font-style: Regular;
            font-size: 24px;
            line-height: 150%;
            letter-spacing: 0%;
          }
        }

        .game-mode {
          display: flex;
          gap: 16px;
        }

        .enygma-wrapper {
          width: fit-content;
          display: flex;
          flex-direction: column;
          align-items: center;

          .enygma-image {
            height: 390px;
            max-width: 276px;
            border: 2px solid #88ffd5;
            box-shadow: 0px 0px 12px 0px #88ffd5;
            opacity: 1;
            border-radius: 138px;
            margin-bottom: 1rem;
          }

          button {
            width: fit-content;
            height: 56px;
            min-width: 104px;
            margin-top: 34px;
            border-radius: 8px;
            padding-top: 12px;
            padding-right: 40px;
            padding-bottom: 12px;
            padding-left: 40px;
            background: #88FFD5;

            // font-family: fontFamily/fontFamily;
            font-weight: 500;
            font-style: normal;
            font-size: 22px;
            line-height: 20px;
            color: black;
          }
        }
      }

      .menu-right {
        height: 100%;
        flex-basis: 121px;
        flex-shrink: 0;
        flex-grow: 0;
        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: 40px;

        div {
          text-align: center;
          font-weight: 700;
          font-style: Bold;
          font-size: 22px;
          line-height: 24px;
          letter-spacing: 0%;
          text-align: center;
          vertical-align: middle;
        }

        img {
          width: 121px;
          height: 121px;
        }
      }
    }
  }

  .rules-modal {
    height: 100%;
    display: flex;
    align-items: center;
  }
}

//   position: relative;
//   width: 100%;
//   height: 100vh;

//   .background {
//     height: 100%;
//     width: 100%;
//     object-fit: cover;
//     position: absolute;
//   }

//   .board-game {
//     position: relative;
//     // width: calc(100% - 160px);
//     // height: calc(100% - 80px);
//     // padding: 40px 80px;

//     .init-game {
//       position: relative;
//       height: 100%;
//       display: flex;
//       flex-direction: column;

//       .top-icons {
//         position: absolute;
//         top: 20px;
//         right: 20px;
//         display: flex;
//         gap: 20px;
//         z-index: 10;

//         .sound-icon,
//         .home-icon {
//           width: 50px;
//           height: 50px;
//           background: rgba(255, 255, 255, 0.9);
//           border-radius: 50%;
//           display: flex;
//           align-items: center;
//           justify-content: center;
//           font-size: 24px;
//           cursor: pointer;
//           box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
//           transition: transform 0.2s ease;

//           &:hover {
//             transform: scale(1.1);
//           }
//         }
//       }
//     }

//     // ========== GAME HEADER FIXED ==========
//     .game-header {
//       height: 104px;
//       outline: 1px solid red;
//       // position: fixed;
//       // top: 0;
//       // left: 0;
//       // right: 0;
//       // z-index: 100;
//       // pointer-events: none; // Permitir clicks a través del header excepto en los elementos interactivos

//       // .back-button {
//       //   position: absolute;
//       //   top: 20px;
//       //   left: 20px;
//       //   background: rgba(255, 255, 255, 0.9);
//       //   color: #333;
//       //   padding: 10px 20px;
//       //   border-radius: 25px;
//       //   font-size: 16px;
//       //   font-weight: 600;
//       //   cursor: pointer;
//       //   box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
//       //   transition: all 0.2s ease;
//       //   pointer-events: auto; // Habilitar clicks en el botón
//       //   display: flex;
//       //   align-items: center;
//       //   gap: 8px;

//       //   &:hover {
//       //     background: rgba(255, 255, 255, 1);
//       //     transform: scale(1.05);
//       //     box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
//       //   }
//       // }

//       .top-icons {
//         position: absolute;
//         top: 20px;
//         right: 20px;
//         display: flex;
//         gap: 20px;
//         z-index: 10;
//         pointer-events: auto; // Habilitar clicks en los iconos

//         .sound-icon,
//         .home-icon {
//           width: 50px;
//           height: 50px;
//           background: rgba(255, 255, 255, 0.9);
//           border-radius: 50%;
//           display: flex;
//           align-items: center;
//           justify-content: center;
//           font-size: 24px;
//           cursor: pointer;
//           box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
//           transition: transform 0.2s ease;

//           &:hover {
//             transform: scale(1.1);
//           }
//         }
//       }

//       .main-content {
//         flex: 1;
//         display: flex;
//         flex-direction: column;
//         align-items: center;
//         justify-content: center;
//         text-align: center;
//         padding: 40px;

//         .game-header {
//           margin-bottom: 40px;

//           h1 {
//             font-family: "Open Sans", sans-serif;
//             font-weight: 700;
//             font-size: 64px;
//             line-height: 1.2;
//             color: variables.$primary-color;
//             margin-bottom: 20px;
//             text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
//           }

//           p {
//             font-family: "Open Sans", sans-serif;
//             font-weight: 400;
//             font-size: 24px;
//             line-height: 1.4;
//             color: variables.$primary-color;
//             max-width: 600px;
//             margin: 0 auto;
//           }
//         }

//         .character-image {
//           margin: 40px 0;

//           .character-circle {
//             width: 300px;
//             height: 300px;
//             border-radius: 50%;
//             border: 6px solid #4ade80;
//             overflow: hidden;
//             margin: 0 auto;
//             box-shadow: 0 8px 32px rgba(74, 222, 128, 0.3);
//             position: relative;

//             img {
//               width: 100%;
//               height: 100%;
//               object-fit: cover;
//             }

//             .character-placeholder {
//               width: 100%;
//               height: 100%;
//               background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
//               display: flex;
//               align-items: center;
//               justify-content: center;
//               font-size: 120px;
//               color: white;
//             }
//           }
//         }

//         .play-button {
//           background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
//           border: none;
//           color: white;
//           font-family: "Open Sans", sans-serif;
//           font-weight: 700;
//           font-size: 24px;
//           padding: 16px 48px;
//           border-radius: 50px;
//           cursor: pointer;
//           box-shadow: 0 4px 20px rgba(74, 222, 128, 0.4);
//           transition: all 0.3s ease;
//           min-width: 200px;

//           &:hover {
//             transform: translateY(-2px);
//             box-shadow: 0 6px 25px rgba(74, 222, 128, 0.5);
//           }

//           &:active {
//             transform: translateY(0);
//           }

//           &:disabled {
//             opacity: 0.7;
//             cursor: not-allowed;
//             transform: none;
//           }
//         }
//       }

//       // ========== BOTTOM NAVIGATION ==========
//       .bottom-navigation {
//         position: absolute;
//         bottom: 30px;
//         left: 30px;
//         right: 30px;
//         display: flex;
//         justify-content: space-around;
//         gap: 20px;

//         @media (max-width: 768px) {
//           gap: 10px;
//           left: 20px;
//           right: 20px;
//         }

//         .nav-icon {
//           display: flex;
//           flex-direction: column;
//           align-items: center;
//           cursor: pointer;
//           transition: transform 0.2s ease;
//           flex: 1;
//           max-width: 120px;

//           &:hover {
//             transform: scale(1.1);
//           }

//           .nav-emoji {
//             width: 60px;
//             height: 60px;
//             background: rgba(255, 255, 255, 0.9);
//             border-radius: 15px;
//             display: flex;
//             align-items: center;
//             justify-content: center;
//             font-size: 30px;
//             margin-bottom: 8px;
//             box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
//             transition: all 0.2s ease;

//             @media (max-width: 768px) {
//               width: 50px;
//               height: 50px;
//               font-size: 24px;
//             }
//           }

//           span {
//             font-family: "Open Sans", sans-serif;
//             font-weight: 600;
//             font-size: 14px;
//             color: variables.$primary-color;
//             text-align: center;

//             @media (max-width: 768px) {
//               font-size: 12px;
//             }
//           }

//           &:hover .nav-emoji {
//             background: rgba(255, 255, 255, 1);
//             box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
//           }
//         }
//       }

//       // ========== SETTINGS VIEW ==========
//       .settings-view {
//         .settings-options {
//           padding: 20px;
//           max-width: 400px;
//           margin: 0 auto;

//           .setting-item {
//             margin-bottom: 25px;
//             padding: 15px;
//             background: rgba(255, 255, 255, 0.9);
//             border-radius: 10px;
//             box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

//             label {
//               display: block;
//               font-weight: 600;
//               margin-bottom: 10px;
//               color: variables.$primary-color;
//             }

//             input[type="range"] {
//               width: 100%;
//               height: 6px;
//               border-radius: 3px;
//               background: #ddd;
//               outline: none;
//               -webkit-appearance: none;
//               appearance: none;

//               &::-webkit-slider-thumb {
//                 -webkit-appearance: none;
//                 appearance: none;
//                 width: 20px;
//                 height: 20px;
//                 border-radius: 50%;
//                 background: variables.$primary-color;
//                 cursor: pointer;
//               }

//               &::-moz-range-thumb {
//                 width: 20px;
//                 height: 20px;
//                 border-radius: 50%;
//                 background: variables.$primary-color;
//                 cursor: pointer;
//                 border: none;
//               }
//             }

//             input[type="checkbox"] {
//               width: 20px;
//               height: 20px;
//               accent-color: variables.$primary-color;
//             }
//           }
//         }
//       }

//       // ========== HELP VIEW ==========
//       .help-view {
//         .help-content {
//           padding: 20px;
//           max-width: 600px;
//           margin: 0 auto;

//           .help-section {
//             margin-bottom: 25px;
//             padding: 20px;
//             background: rgba(255, 255, 255, 0.9);
//             border-radius: 10px;
//             box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

//             h3 {
//               color: variables.$primary-color;
//               margin-bottom: 10px;
//               font-size: 1.2rem;
//             }

//             p {
//               color: #666;
//               line-height: 1.6;
//               margin: 0;
//             }
//           }
//         }
//       }

//       .rules-modal {
//         display: flex;
//         justify-content: center;
//         align-items: center;
//         // height: 100vh;

//     width: calc(100% - 160px);
//     height: calc(100% - 80px);
//     padding: 40px 80px;

//         .rules-content {
//           background: white;
//           border-radius: 20px;
//           padding: 30px;
//           max-width: 600px;
//           max-height: 80vh;
//           overflow-y: auto;
//           box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
//           position: relative;

//           .rules-header {
//             display: flex;
//             justify-content: space-between;
//             align-items: center;
//             margin-bottom: 20px;
//             border-bottom: 2px solid #f0f0f0;
//             padding-bottom: 15px;

//             h2 {
//               font-family: "Open Sans", sans-serif;
//               font-weight: 700;
//               font-size: 28px;
//               color: variables.$primary-color;
//               margin: 0;
//             }

//             .close-button {
//               background: none;
//               border: none;
//               font-size: 24px;
//               cursor: pointer;
//               color: #666;
//               padding: 5px;
//               border-radius: 50%;
//               width: 40px;
//               height: 40px;
//               display: flex;
//               align-items: center;
//               justify-content: center;
//               transition: background-color 0.2s ease;

//               &:hover {
//                 background-color: #f0f0f0;
//               }
//             }
//           }

//           .rules-body {
//             h3 {
//               font-family: "Open Sans", sans-serif;
//               font-weight: 600;
//               font-size: 20px;
//               color: variables.$primary-color;
//               margin: 20px 0 10px 0;
//             }

//             ul {
//               margin: 0 0 20px 0;
//               padding-left: 20px;

//               li {
//                 font-family: "Open Sans", sans-serif;
//                 font-size: 16px;
//                 line-height: 1.6;
//                 color: #333;
//                 margin-bottom: 8px;
//               }
//             }
//           }
//         }
//       }
//     }
//   }
// }
